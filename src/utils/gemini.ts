import { GoogleGenerativeA<PERSON>, Tool } from '@google/generative-ai';
import { DEFAULT_MODEL, getModelById } from '../config/models';

export interface GeminiResponse {
  content: string;
  sources?: Array<{
    title: string;
    uri: string;
  }>;
}

// Define interfaces for grounding data to avoid 'any'
interface GroundingChunk {
  web: {
    title: string;
    uri: string;
  };
}



export const generateGeminiResponse = async (
  userMessage: string,
  apiKey: string,
  searchGrounding: boolean = false,
  selectedModelId?: string
): Promise<GeminiResponse> => {
  if (!apiKey) {
    // Fallback to mock response if API key is not configured
    const mockResponses = [
      "I'd be happy to help you explore this topic. However, I notice the Gemini API key isn't configured yet.",
      "This is a mock response. To get real AI responses, please configure your Gemini API key.",
      "Interesting question! Please set up your Gemini API key to get personalized AI responses.",
      "I can see you're asking about this topic. Configure the Gemini API to unlock full AI capabilities.",
    ];
    
    return {
      content: mockResponses[Math.floor(Math.random() * mockResponses.length)],
      sources: searchGrounding ? [
        { title: 'Mock Source 1', uri: 'https://example.com/mock-source-1' },
        { title: 'Mock Source 2', uri: 'https://example.com/mock-source-2' }
      ] : undefined
    };
  }

  // Determine which model to use
  const modelConfig = selectedModelId ? getModelById(selectedModelId) : DEFAULT_MODEL;
  
  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    const modelId = modelConfig?.id || DEFAULT_MODEL.id;

    // Create the model with the selected configuration
    const model = genAI.getGenerativeModel({
      model: modelId,
      tools: searchGrounding ? [{ googleSearchRetrieval: {} }] as Tool[] : undefined
    });

    // Configure generation settings based on the selected model
    const generationConfig = {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: Math.min(modelConfig?.maxTokens || 2048, 2048), // Use model's max tokens or default
    };

    // Enhanced prompt for search grounding
    let prompt = userMessage;
    if (searchGrounding) {
      prompt = `Please provide a comprehensive and well-researched response to the following question. Use current information and cite relevant sources when available: ${userMessage}`;
    }

    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig,
    });

    const response = await result.response;
    const text = response.text();

    // Extract sources from grounding metadata
    let sources: Array<{ title: string; uri: string }> | undefined;
    
    if (searchGrounding) {
      try {
        // Access the full response to get grounding metadata
        const fullResponse = result.response;
        const candidates = fullResponse.candidates;
        
        if (candidates && candidates[0] && candidates[0].groundingMetadata) {
          const groundingMetadata = candidates[0].groundingMetadata;
          
          // Safely access groundingChunks with a runtime check and type assertion
          if ('groundingChunks' in groundingMetadata) {
            const chunks = (groundingMetadata as { groundingChunks: GroundingChunk[] }).groundingChunks;

            if (chunks && chunks.length > 0) {
              sources = chunks
                .filter((chunk) => chunk.web && chunk.web.title && chunk.web.uri)
                .map((chunk) => ({
                  title: chunk.web.title,
                  uri: chunk.web.uri
                }))
                // Remove duplicates based on URI
                .filter((source, index, self) => 
                  self.findIndex(s => s.uri === source.uri) === index
                );
            }
          }
        }
      } catch (error) {
        console.warn('Could not extract grounding sources:', error);
        // Fallback: if we can't extract proper sources but response seems grounded
        if (text.length > 200) {
          sources = [{ title: 'Search-grounded response', uri: '' }];
        }
      }
    }

    return {
      content: text,
      sources
    };

  } catch (error) {
    console.error('Error calling Gemini API:', error);

    // Provide helpful error messages
    let errorMessage = 'Sorry, I encountered an error while processing your request.';

    if (error instanceof Error) {
      if (error.message.includes('API_KEY') || error.message.includes('401')) {
        errorMessage = 'Please check your Gemini API key configuration. Make sure it\'s valid and has the necessary permissions.';
      } else if (error.message.includes('quota') || error.message.includes('429')) {
        errorMessage = 'API quota exceeded. Please check your Gemini API usage limits or try again later.';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (error.message.includes('model') || error.message.includes('404')) {
        const modelName = modelConfig?.name || 'the selected model';
        errorMessage = `Model access error. Please ensure your API key has access to ${modelName}. You may need to try a different model.`;
      } else if (error.message.includes('SAFETY') || error.message.includes('safety')) {
        errorMessage = 'Content was blocked by safety filters. Please try rephrasing your question.';
      } else if (error.message.includes('RECITATION') || error.message.includes('recitation')) {
        errorMessage = 'Response was blocked due to potential copyright concerns. Please try a different question.';
      }
    }

    return {
      content: errorMessage,
      sources: undefined
    };
  }
};