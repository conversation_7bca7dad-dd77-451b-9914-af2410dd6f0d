# Search Grounding Fix Test Plan

## Issue Fixed
The search grounding functionality was broken after implementing the model switcher because Gemini 2.5 models have limited/no support for the `googleSearchRetrieval` tool.

## Solution Implemented
1. **Added model compatibility flags** - Added `supportsSearchGrounding` property to model definitions
2. **Updated model configuration** - Added Gemini 1.5 models that support search grounding
3. **Implemented compatibility checks** - Modified gemini.ts to only enable search grounding for compatible models
4. **Updated UI feedback** - Modified Node and ModelSelector components to show compatibility status

## Test Steps

### 1. Test Model Selector
- [ ] Open the application
- [ ] Click on the model selector dropdown
- [ ] Verify that Gemini 1.5 models show a search icon (🔍) indicating search grounding support
- [ ] Verify that Gemini 2.5 models do NOT show the search icon
- [ ] Select a Gemini 1.5 model (e.g., Gemini 1.5 Flash)

### 2. Test Search Grounding with Compatible Model
- [ ] With Gemini 1.5 Flash selected, create a new node
- [ ] Click the search grounding toggle button (🔍) - it should be enabled
- [ ] Verify the button turns blue when enabled
- [ ] Enter a query that would benefit from search grounding (e.g., "What's the weather in New York today?")
- [ ] Submit the query
- [ ] Verify that the response includes sources/citations if search grounding worked

### 3. Test Search Grounding with Incompatible Model
- [ ] Switch to a Gemini 2.5 model (e.g., Gemini 2.5 Flash)
- [ ] Create a new node or use existing node
- [ ] Verify the search grounding toggle button (🔍) is disabled/grayed out
- [ ] Hover over the button to see tooltip: "Search Grounding not supported by current model"
- [ ] If search grounding was previously enabled, verify it shows "Search not available for this model" message

### 4. Test Model Switching Behavior
- [ ] Enable search grounding on a node with Gemini 1.5 Flash
- [ ] Switch to Gemini 2.5 Flash
- [ ] Verify the search grounding toggle becomes disabled
- [ ] Switch back to Gemini 1.5 Flash
- [ ] Verify the search grounding toggle becomes enabled again

## Expected Results
- ✅ Search grounding works with Gemini 1.5 models
- ✅ Search grounding is properly disabled for Gemini 2.5 models
- ✅ UI clearly indicates which models support search grounding
- ✅ No errors when switching between models
- ✅ User gets clear feedback about feature availability
